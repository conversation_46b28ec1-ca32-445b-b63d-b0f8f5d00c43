<?php

namespace App\Console\Commands;

use App\Models\SIInvitation;
use Illuminate\Console\Command;

class CreateInvitationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invitation:create {email} {contrato_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Crear una nueva invitación para testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $contratoId = $this->argument('contrato_id');

        // Verificar si ya existe una invitación pendiente para este email
        $existingInvitation = SIInvitation::findPendingByEmail($email);
        
        if ($existingInvitation) {
            $this->error("Ya existe una invitación pendiente para el email: {$email}");
            $this->info("Token existente: {$existingInvitation->token}");
            $this->info("URL: " . route('register.with.token', $existingInvitation->token));
            return 1;
        }

        // Crear nueva invitación
        $invitation = new SIInvitation([
            'contrato_id' => $contratoId,
            'email' => $email,
            'status' => SIInvitation::STATUS_PENDING,
            'token' => SIInvitation::generateToken(),
            'bitacora' => [
                now()->toISOString() => [
                    'action' => 'created',
                    'created_by' => 'console_command'
                ]
            ]
        ]);

        $invitation->save();

        $this->info("Invitación creada exitosamente!");
        $this->info("Email: {$email}");
        $this->info("Contrato ID: {$contratoId}");
        $this->info("Token: {$invitation->token}");
        $this->info("URL de registro: " . route('register.with.token', $invitation->token));

        return 0;
    }
}

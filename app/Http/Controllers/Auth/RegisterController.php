<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Livewire\Auth\Register;
use App\Models\SIInvitation;
use Illuminate\Http\Request;
use Illuminate\View\View;

class RegisterController extends Controller
{
    /**
     * Mostrar el formulario de registro sin token
     */
    public function showRegistrationForm(): View
    {
        return view('auth.register');
    }

    /**
     * Mostrar el formulario de registro con token de invitación
     */
    public function showRegistrationFormWithToken(Request $request, string $token)
    {
        // Buscar la invitación por token
        $invitation = SIInvitation::findByToken($token);

        // Verificar que la invitación existe y está pendiente
        if (!$invitation || !$invitation->isPending()) {
            // Si el token no es válido, redirigir al registro normal con mensaje
            return redirect()->route('register')
                ->with('error', 'El enlace de invitación no es válido o ha expirado.');
        }

        // Pasar los datos de la invitación a la sesión para que el componente los pueda usar
        session([
            'invitation_data' => [
                'token' => $token,
                'email' => $invitation->email,
                'contrato_id' => $invitation->contrato_id,
                'invitation_id' => $invitation->id
            ]
        ]);

        return view('auth.register');
    }
}

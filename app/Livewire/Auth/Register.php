<?php

namespace App\Livewire\Auth;

use App\Models\SIInvitation;
use App\Models\User;
use App\Services\RecaptchaService;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Livewire\Component;

class Register extends Component
{
    public string $firstname = '';
    public string $lastname = '';
    public string $username = '';
    public string $email = '';
    public string $phone_country_code = '';
    public string $phone_number = '';
    public string $password = '';
    public string $password_confirmation = '';
    public ?bool $usernameAvailable = null;
    public bool $checkingUsername = false;
    public string $recaptchaResponse = '';
    public bool $recaptchaVerified = false;

    // Datos de invitación
    public ?string $invitationToken = null;
    public ?int $invitationId = null;
    public ?int $contratoId = null;
    public bool $isFromInvitation = false;
    public ?array $templateData = null;

    // Datos extraídos del template para personalización
    public ?string $inviterName = null;
    public ?string $inviterCompany = null;
    public ?string $inviterPhone = null;
    public ?string $inviteeName = null;
    public ?string $originalInvitationEmail = null;
    public bool $emailChanged = false;

    /**
     * Inicializar el componente
     */
    public function mount(): void
    {
        // Verificar si hay datos de invitación en la sesión
        $invitationData = session('invitation_data');

        if ($invitationData) {
            $this->setInvitationData($invitationData);
            // Limpiar los datos de la sesión después de usarlos
            session()->forget('invitation_data');
        }
    }

    /**
     * Establecer los datos de la invitación
     */
    public function setInvitationData(array $data): self
    {
        $this->invitationToken = $data['token'] ?? null;
        $this->invitationId = $data['invitation_id'] ?? null;
        $this->contratoId = $data['contrato_id'] ?? null;
        $this->isFromInvitation = !empty($this->invitationToken);
        $this->templateData = $data['template_data'] ?? null;

        // Pre-llenar el email si viene de la invitación
        if (!empty($data['email'])) {
            $this->originalInvitationEmail = $data['email'];
            $this->email = $data['email'];
        }

        // Extraer datos del template para personalización
        if ($this->templateData) {
            $this->inviterName = $this->templateData['nombre_remitente'] ?? null;
            $this->inviterCompany = $this->templateData['empresa_remitente'] ?? null;
            $this->inviterPhone = $this->templateData['telefono'] ?? null;
            $this->inviteeName = $this->templateData['nombre'] ?? null;

            // Pre-llenar campos si tenemos datos
            $this->prefillFieldsFromTemplate();
        }

        return $this;
    }

    /**
     * Pre-llenar campos del formulario con datos del template
     */
    private function prefillFieldsFromTemplate(): void
    {
        if (!$this->templateData) {
            return;
        }

        // Extraer nombre y apellidos del nombre completo del invitado
        if (!empty($this->templateData['nombre']) && empty($this->firstname) && empty($this->lastname)) {
            $nameParts = explode(' ', trim($this->templateData['nombre']), 2);
            $this->firstname = $nameParts[0] ?? '';
            $this->lastname = $nameParts[1] ?? '';

            // Generar username a partir del nombre
            if (!empty($this->firstname) || !empty($this->lastname)) {
                $this->generateUsernameFromNames();
            }
        }

        // Pre-llenar teléfono si está disponible
        if (!empty($this->templateData['telefono_destinatario']) && empty($this->phone_number)) {
            $phone = $this->templateData['telefono_destinatario'];

            // Intentar extraer código de país y número
            if (preg_match('/^(\+\d{1,3})\s*(.+)$/', $phone, $matches)) {
                $this->phone_country_code = $matches[1];
                $this->phone_number = preg_replace('/\D/', '', $matches[2]);
            }
        }
    }

    /**
     * Restaurar el email original de la invitación
     */
    public function restoreOriginalEmail(): void
    {
        if ($this->isFromInvitation && $this->originalInvitationEmail) {
            $this->email = $this->originalInvitationEmail;
            $this->emailChanged = false;
            $this->resetErrorBag('email');

            // Mostrar mensaje de confirmación mejorado
            $this->dispatch('email-restored',
                '✓ Email restaurado correctamente. Ahora te registrarás como socio de ' . ($this->inviterName ?? 'tu invitador') . '.'
            );

            // Forzar la actualización del input de email
            $this->dispatch('update-email-input', $this->originalInvitationEmail);
        }
    }

    /**
     * Update both firstname and lastname, then generate username
     */
    public function updatedFirstname(): void
    {
        $this->resetErrorBag('firstname');
        $this->generateUsernameFromNames();
    }

    /**
     * Update both firstname and lastname, then generate username
     */
    public function updatedLastname(): void
    {
        $this->resetErrorBag('lastname');
        $this->generateUsernameFromNames();
    }

    public function updatedEmail(): void
    {
        $this->resetErrorBag('email');

        // Verificar si el email cambió respecto al original de la invitación
        if ($this->isFromInvitation && $this->originalInvitationEmail) {
            $this->emailChanged = ($this->email !== $this->originalInvitationEmail);
        }
    }

    public function updatedPhoneCountryCode(): void
    {
        $this->resetErrorBag('phone_country_code');
    }

    public function updatedPhoneNumber(): void
    {
        $this->resetErrorBag('phone_number');
    }

    /**
     * Generate a username from firstname and lastname
     */
    private function generateUsernameFromNames(): void
    {
        if (empty($this->username) && !empty($this->firstname) && !empty($this->lastname)) {
            $fullname = $this->firstname . ' ' . $this->lastname;
            $username = Str::slug($fullname);
            $this->username = $username;
            $this->checkUsernameAvailability();
        }
    }

    /**
     * Format username input to valid format (server-side fallback)
     */
    public function updatedUsername($value): void
    {
        // Asegurar que value sea un string
        if (!is_string($value)) {
            $value = (string)$value;
            $this->username = $value;
        }

        // Convertir a slug
        $this->username = Str::slug($value);

        // Verificar disponibilidad
        $this->checkUsernameAvailability();

        // Emitir evento para que la vista actualice el input
        $this->dispatch('username-processed', username: $this->username);
    }

    /**
     * Check if the username is available
     */
    public function checkUsernameAvailability(): void
    {
        if (empty($this->username)) {
            $this->usernameAvailable = null;
            return;
        }

        $this->checkingUsername = true;
        $this->usernameAvailable = null;

        // Verificamos directamente
        $this->performUsernameCheck();
    }

    /**
     * Perform the actual username availability check
     */
    public function performUsernameCheck(): void
    {
        if (empty($this->username)) {
            $this->checkingUsername = false;
            $this->usernameAvailable = null;
            return;
        }

        // Asegurar que username sea un string
        $this->username = (string)$this->username;

        // Verificamos si el nombre de usuario ya existe en la base de datos
        $exists = User::where('usuario', $this->username)->exists();
        $this->usernameAvailable = !$exists;
        $this->checkingUsername = false;
    }

    /**
     * Handle an incoming registration request.
     *
     * @return RedirectResponse|void
     */
    public function register(RecaptchaService $recaptchaService)
    {
        $validated = $this->validate(
            [
                'firstname' => ['required', 'string', 'max:100'],
                'lastname' => ['required', 'string', 'max:100'],
                'username' => [
                    'required',
                    'string',
                    'min:3',
                    'max:30',
                    'alpha_dash',
                    'unique:' . User::class . ',usuario'
                ],
                'email' => [
                    'required',
                    'string',
                    'lowercase',
                    'email',
                    'max:255',
                    'unique:' . User::class . ',logmail'
                ],
                'phone_country_code' => ['required', 'string', 'max:5'],
                'phone_number' => ['required', 'string', 'max:20'],
                'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            ],
            [
                'firstname.required' => 'Por favor ingresa tu nombre',
                'lastname.required' => 'Por favor ingresa tus apellidos',
                'username.required' => 'Por favor ingresa un nombre de usuario',
                'username.unique' => 'El nombre de usuario "' . $this->username . '" ya está en uso',
                'email.required' => 'Por favor ingresa tu correo electrónico',
                'email.email' => 'Por favor ingresa un correo electrónico válido',
                'email.unique' => 'Este correo electrónico ya está registrado como logmail',
                'phone_country_code.required' => 'Por favor selecciona un código de país',
                'phone_number.required' => 'Por favor ingresa tu número de teléfono',
                'password.required' => 'Por favor ingresa una contraseña',
                'password.confirmed' => 'Las contraseñas no coinciden',
            ],
        );

        $validated['password'] = Hash::make($validated['password']);

        // Validar el captcha solo si NO es una invitación (los tokens son suficiente validación)
        if (!$this->isFromInvitation && !$this->recaptchaVerified) {
            if (empty($this->recaptchaResponse)) {
                $this->addError('recaptcha', 'Por favor, completa el captcha.');
                return;
            }

            // Verificar la respuesta del captcha
            if (!$recaptchaService->validate($this->recaptchaResponse)) {
                $this->addError('recaptcha', 'La verificación del captcha ha fallado. Por favor, inténtalo de nuevo.');
                return;
            }

            Log::info('Captcha verified successfully.');
            $this->recaptchaVerified = true;
        } elseif ($this->isFromInvitation) {
            // Para invitaciones, el token es suficiente validación
            Log::info('Captcha omitido para registro por invitación', [
                'invitation_token' => $this->invitationToken
            ]);
        }

        // Crear el campo name a partir de firstname y lastname
        $validated['logmail'] = $validated['email']; // Guardar el email como logmail
        $validated['name'] = $validated['firstname'] . ' ' . $validated['lastname'];
        $validated['nombre'] = $validated['firstname'];
        $validated['apellidos'] = $validated['lastname'];
        $validated['usuario'] = $validated['username'];
        $validated['telefono'] = $validated['phone_country_code'] . ' ' . $validated['phone_number'];
        $validated['phone_number'] = preg_replace('/\D/', '', $validated['phone_number']);
        $validated['activo'] = 'Si';
        $validated['quien_registro'] = 'SistemaInmobiliario';

        // Eliminar firstname y lastname
        unset($validated['firstname'], $validated['lastname'], $validated['username']);

        event(new Registered(($user = User::create($validated))));

        // Procesar la invitación si existe y el email no cambió
        if ($this->isFromInvitation && $this->invitationId && !$this->emailChanged) {
            $this->processInvitation($user);
        } elseif ($this->isFromInvitation && $this->emailChanged) {
            // Registrar en logs que el usuario cambió el email
            Log::info('Usuario registrado con email diferente al de la invitación', [
                'invitation_id' => $this->invitationId,
                'original_email' => $this->originalInvitationEmail,
                'new_email' => $this->email,
                'user_id' => $user->id
            ]);
        }

        Auth::login($user);

        // Redirección directa en lugar de usar navigate: true
        return redirect()->intended(route('dashboard'));
    }

    /**
     * Procesar la invitación cuando el usuario se registra
     */
    private function processInvitation(User $user): void
    {
        try {
            $invitation = SIInvitation::find($this->invitationId);

            if ($invitation && $invitation->isPending()) {
                // Marcar la invitación como aceptada
                $invitation->markAsAccepted();

                // Agregar información a la bitácora
                $invitation->addToBitacora([
                    'action' => 'user_registered',
                    'user_id' => $user->id,
                    'user_email' => $user->logmail,
                    'user_username' => $user->usuario,
                    'timestamp' => now()->toISOString()
                ]);

                $invitation->save();

                // Guardar datos de invitación en la sesión para usar en el proceso de activación
                session([
                    'invitation_data_for_activation' => [
                        'invitation_id' => $this->invitationId,
                        'contrato_id' => $this->contratoId,
                        'inviter_name' => $this->inviterName,
                        'inviter_company' => $this->inviterCompany,
                        'original_email' => $this->originalInvitationEmail,
                        'processed_at' => now()->toISOString()
                    ]
                ]);

                Log::info('Invitación procesada exitosamente', [
                    'invitation_id' => $this->invitationId,
                    'user_id' => $user->id,
                    'contrato_id' => $this->contratoId,
                    'datos_guardados_en_sesion' => true
                ]);
            }
        } catch (Exception $e) {
            Log::error('Error al procesar la invitación', [
                'invitation_id' => $this->invitationId,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function render(): View
    {
        return view('livewire.auth.register')
            ->layout('components.layouts.auth-meta');
    }
}
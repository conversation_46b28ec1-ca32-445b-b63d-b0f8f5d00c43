<div>
    <div class="flex flex-col gap-6">
        <div class="text-center space-y-2">
            <flux:heading size="lg" class="font-semibold text-indigo-600 dark:text-indigo-400">
                {{ __('Sistema Inmobiliario') }}</flux:heading>
            <flux:heading size="xl" class="font-bold">{{ __('Crea tu portal inmobiliario') }}
            </flux:heading>
            <p class="text-muted mt-1 text-zinc-600 dark:text-zinc-400">
                {{ __('Únete a la red de profesionales inmobiliarios y expande tu negocio') }}</p>
        </div>

        <!-- Session Status -->
        <x-auth-session-status class="text-center" :status="session('status')"/>

        <!-- Mensaje de invitación -->
        @if ($isFromInvitation)
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-5">
                <div class="flex items-start space-x-4 py-2 bg-blue-50 dark:bg-blue-900/20">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="currentColor"
                                 viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-semibold text-blue-900 dark:text-blue-100">
                            {{ __('Invitación a la Multibolsa Inmobiliaria') }}
                        </h3>

                        @if ($inviterName)
                            <p class="text-sm text-blue-800 dark:text-blue-200 mt-1">
                                <span class="font-medium">{{ $inviterName }}</span>
                                @if ($inviterCompany)
                                    {{ __('de') }} <span class="font-medium">{{ $inviterCompany }}</span>
                                @endif
                                {{ __('te ha invitado a formar parte de su red profesional.') }}
                            </p>
                        @else
                            <p class="text-sm text-blue-800 dark:text-blue-200 mt-1">
                                {{ __('Has sido invitado a unirte a Sistema Inmobiliario. Completa tu registro para comenzar.') }}
                            </p>
                        @endif

                        @if ($inviterPhone)
                            <p class="text-xs text-blue-600 dark:text-blue-300 mt-2">
                                <span class="inline-flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                    </svg>
                                    {{ __('Contacto:') }} {{ $inviterPhone }}
                                </span>
                            </p>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        <div
                class="bg-white dark:bg-neutral-800 p-6 rounded-lg shadow-md border border-zinc-200 dark:border-zinc-700">
            <form wire:submit.prevent="register" class="flex flex-col gap-6">

                <!-- Name & Lastname -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <flux:input wire:model.blur="firstname" :label="__('Nombre')" type="text"
                                    autofocus autocomplete="given-name" :placeholder="__('Nombre')"
                                    class="bg-zinc-50 dark:bg-neutral-700"/>
                    </div>
                    <div>
                        <flux:input wire:model.blur="lastname" :label="__('Apellidos')" type="text"
                                    autocomplete="family-name" :placeholder="__('Apellidos')"
                                    class="bg-zinc-50 dark:bg-neutral-700"/>
                    </div>
                </div>

                <!-- Username -->
                <div>
                    <div class="relative">
                        <label for="username"
                               class="block text-sm font-medium leading-6 text-gray-900 dark:text-gray-100">
                            {{ __('Nombre de usuario / agencia') }}
                        </label>
                        <div class="mt-2">
                            <input
                                    type="text"
                                    id="username"

                                    autocomplete="off"
                                    wire:model.change="username"
                                    placeholder="{{ !empty($username) ? $username : 'nombre-usuario' }}"
                                    class="bg-zinc-50 dark:bg-neutral-700 block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset   dark:text-white placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
                                    @error('username') ring-red-500 @else ring-gray-300 dark:ring-gray-600 @enderror"
                                    wire:target="performUsernameCheck"
                                    wire:loading.class="opacity-50"
                            >
                        </div>

                        <script>
                            document.addEventListener('livewire:initialized', () => {
                                @this.
                                on('username-processed', ({username}) => {
                                    // Actualizar el valor del input
                                    document.getElementById('username').value = username;
                                });
                            });
                        </script>

                        @error('username')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror

                        <!-- Indicador de disponibilidad -->
                        <div class="absolute right-3 top-9">
                            <!-- Cargando -->
                            <div wire:loading wire:target="performUsernameCheck" class="text-gray-500">
                                <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg"
                                     fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                            stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                            </div>

                            <!-- Disponible -->
                            @if ($usernameAvailable === true)
                                <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg"
                                     viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                          clip-rule="evenodd"/>
                                </svg>
                            @endif

                            <!-- No disponible -->
                            @if ($usernameAvailable === false)
                                <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                     viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                          clip-rule="evenodd"/>
                                </svg>
                            @endif
                        </div>
                    </div>

                    <!-- Mensaje de disponibilidad -->
                    @if ($usernameAvailable === true)
                        <p class="mt-1 text-sm text-green-600 dark:text-green-400">
                            {{ __('¡Nombre de usuario disponible!') }}</p>
                    @elseif($usernameAvailable === false)
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ __('El nombre de usuario "') }}{{ $username }}{{ __('" ya está en uso.') }}</p>
                    @endif

                    <!-- Vista previa del portal web - Solo mostrar cuando el nombre de usuario está confirmado como disponible -->
                    @if ($usernameAvailable === true)
                        <div class="mt-2 flex items-center text-sm">
                            <span class="text-zinc-600 dark:text-zinc-400">Tu portal web: </span>
                            <span class="ml-1 font-medium text-indigo-600 dark:text-indigo-400">
                                <span>https://{{ $username }}.mulbin.com</span>
                            </span>
                            <div class="relative ml-1" x-data="{ open: false }">
                                <button type="button" @click="open = !open" @click.away="open = false"
                                        class="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                         fill="currentColor" class="w-4 h-4">
                                        <path fill-rule="evenodd"
                                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                </button>
                                <!-- Popover/Tooltip -->
                                <div x-show="open" x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0 translate-y-1"
                                     x-transition:enter-end="opacity-100 translate-y-0"
                                     x-transition:leave="transition ease-in duration-150"
                                     x-transition:leave-start="opacity-100 translate-y-0"
                                     x-transition:leave-end="opacity-0 translate-y-1"
                                     class="absolute right-0 z-10 mt-2 w-72 rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-gray-700 focus:outline-none"
                                     style="display: none;">
                                    <div class="p-4">
                                        <h3
                                                class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                            {{ __('Información sobre tu portal web') }}
                                        </h3>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">
                                            {{ __('Tu portal web inmobiliario estará inicialmente disponible como un subdominio de mulbin.com utilizando tu nombre de usuario.') }}
                                        </p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">
                                            {{ __('Posteriormente, podrás configurar tu propio dominio personalizado a través de nuestras opciones de complementos.') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Email Address -->
                <div>
                    <flux:input wire:model.blur="email" :label="__('Correo electrónico')" type="email"
                                autocomplete="email" placeholder="<EMAIL>"
                                class="bg-zinc-50 dark:bg-neutral-700"/>

                    @if ($isFromInvitation && !$emailChanged)
                        <div class="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-green-600 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <p class="text-xs text-green-700 dark:text-green-300">
                                    {{ __('Perfecto! Te registrarás automáticamente como socio de') }} <span class="font-medium">{{ $inviterName ?? 'tu invitador' }}</span>
                                </p>
                            </div>
                        </div>
                    @endif

                    @if ($isFromInvitation && $emailChanged)
                        <div class="mt-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                            <div class="flex items-start">
                                <svg class="w-4 h-4 text-amber-600 dark:text-amber-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <div class="flex-1">
                                    <p class="text-xs font-medium text-amber-800 dark:text-amber-200">
                                        {{ __('Email modificado - Registro independiente') }}
                                    </p>
                                    <p class="text-xs text-amber-700 dark:text-amber-300 mt-1">
                                        {{ __('La invitación fue enviada a') }} <span class="font-mono bg-amber-100 dark:bg-amber-800 px-1 rounded">{{ $originalInvitationEmail }}</span>.
                                        {{ __('Al usar un email diferente, tu registro será independiente y deberás solicitar manualmente la conexión con') }}
                                        <span class="font-medium">{{ $inviterName ?? 'tu invitador' }}</span> {{ __('desde tu panel una vez registrado.') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if ($isFromInvitation && $emailChanged)
                        <div class="mt-2">
                            <button type="button"
                                    wire:click="restoreOriginalEmail"
                                    class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 underline transition-colors duration-200">
                                {{ __('Usar email original de la invitación') }} ({{ $originalInvitationEmail }})
                            </button>
                        </div>
                    @endif
                </div>

                <!-- Phone Number with Country Code -->
                <div>
                    <label for="phone_combo"
                           class="block text-sm font-medium leading-6 text-gray-900 dark:text-gray-100">
                        {{ __('Número de teléfono') }}
                    </label>
                    <div class="relative mt-2 rounded-md">
                        <div class="grid grid-cols-5 gap-2">
                            <div class="col-span-2 relative">
                                <select wire:model.blur="phone_country_code" id="phone_country_code"
                                        class="bg-zinc-50 dark:bg-neutral-700 block w-full rounded-md border-0 py-2.5 pl-3 pr-8 text-gray-900 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 dark:text-white placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 appearance-none @error('phone_country_code') ring-red-500 border-red-500 @enderror">
                                    <option value="">🌐 {{ __('Seleccionar') }}</option>
                                    <option value="+52">🇲🇽 +52 (MX)</option>
                                    <option value="+1">🇺🇸 +1 (US)</option>
                                    <option value="+1">🇨🇦 +1 (CA)</option>
                                    <option value="+34">🇪🇸 +34 (ES)</option>
                                    <option value="+57">🇨🇴 +57 (CO)</option>
                                    <option value="+54">🇦🇷 +54 (AR)</option>
                                    <option value="+56">🇨🇱 +56 (CL)</option>
                                    <option value="+51">🇵🇪 +51 (PE)</option>
                                    <option value="+58">🇻🇪 +58 (VE)</option>
                                    <option value="+593">🇪🇨 +593 (EC)</option>
                                    <option value="+502">🇬🇹 +502 (GT)</option>
                                    <option value="+503">🇸🇻 +503 (SV)</option>
                                    <option value="+504">🇭🇳 +504 (HN)</option>
                                    <option value="+505">🇳🇮 +505 (NI)</option>
                                    <option value="+506">🇨🇷 +506 (CR)</option>
                                    <option value="+507">🇵🇦 +507 (PA)</option>
                                    <option value="+591">🇧🇴 +591 (BO)</option>
                                    <option value="+595">🇵🇾 +595 (PY)</option>
                                    <option value="+598">🇺🇾 +598 (UY)</option>
                                    <option value="+55">🇧🇷 +55 (BR)</option>
                                    <option value="+351">🇵🇹 +351 (PT)</option>
                                    <option value="+44">🇬🇧 +44 (UK)</option>
                                    <option value="+33">🇫🇷 +33 (FR)</option>
                                    <option value="+49">🇩🇪 +49 (DE)</option>
                                    <option value="+39">🇮🇹 +39 (IT)</option>
                                </select>
                                <div
                                        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <svg class="h-4 w-4 text-gray-400"
                                         xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                         fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                              d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                @error('phone_country_code')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">
                                    {{ __('Por favor selecciona un código de país') }}</p>
                                @enderror
                            </div>
                            <div class="col-span-3">
                                <input type="tel" wire:model.blur="phone_number" id="phone_number"
                                       class="bg-zinc-50 dark:bg-neutral-700 block w-full rounded-md border-0 py-2.5 pl-3 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 dark:text-white placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('phone_number') ring-red-500 @enderror"
                                       placeholder="************" inputmode="numeric"
                                       x-mask="************" maxlength="12">
                            </div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            {{ __('Selecciona el código de país e ingresa tu número sin el código.') }}
                        </p>
                        @error('phone_number')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Password -->
                <flux:input wire:model="password" :label="__('Contraseña')" type="password"
                            autocomplete="new-password" :placeholder="__('Contraseña')"
                            class="bg-zinc-50 dark:bg-neutral-700"/>

                <!-- Confirm Password -->
                <flux:input wire:model="password_confirmation" :label="__('Confirmar contraseña')"
                            type="password" autocomplete="new-password"
                            :placeholder="__('Confirmar contraseña')" class="bg-zinc-50 dark:bg-neutral-700"/>

                <!-- Google reCAPTCHA -->
                <div class="mt-4">
                    <div wire:ignore>
                        <div id="recaptcha-container" class="flex justify-center"></div>
                    </div>
                    @error('recaptcha')
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400 text-center">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex items-center justify-end">
                    <flux:button type="submit" variant="primary"
                                 class="w-full bg-indigo-600 hover:bg-indigo-700 cursor-pointer">
                        {{ __('Crear cuenta') }}
                    </flux:button>
                </div>

                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white dark:bg-neutral-800 text-gray-500 dark:text-gray-400">
                            {{ __('O regístrate con') }}
                        </span>
                    </div>
                </div>

                <div class="flex flex-col space-y-3">
                    <a href="{{ route('auth.google') }}"
                       class="flex w-full items-center justify-center gap-3 rounded-md bg-white dark:bg-neutral-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-neutral-600 focus-visible:ring-transparent">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                  fill="#4285F4"/>
                            <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                  fill="#34A853"/>
                            <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                  fill="#FBBC05"/>
                            <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                  fill="#EA4335"/>
                        </svg>
                        {{ __('Continuar con Google') }}
                    </a>

                    <a href="{{ route('auth.facebook') }}"
                       class="flex w-full items-center justify-center gap-3 rounded-md bg-[#1877F2] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#0C63D4] focus-visible:ring-transparent">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        {{ __('Continuar con Facebook') }}
                    </a>
                </div>
            </form>
        </div>

        <div
                class="bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg border border-indigo-100 dark:border-indigo-800 space-y-2">
            <p class="text-sm text-indigo-700 dark:text-indigo-300 font-medium">
                {{ __('Beneficios de registrarte') }}</p>
            <ul class="text-xs text-indigo-600 dark:text-indigo-400 list-disc pl-5 space-y-1">
                <li>{{ __('Acceso inmediato para registrar tus inmuebles con fotos y vistas de 360°') }}
                </li>
                <li>{{ __('Conecta con otros profesionales y comienza a hacer negocios') }}</li>
                <li>{{ __('Tus inmuebles serán promovidos por medio de la Multibolsa Inmobiliaria') }}
                </li>
                <li>{{ __('Tu portal web personalizado se activará en 24 horas') }}</li>
            </ul>
        </div>

        <div class="space-x-1 rtl:space-x-reverse text-center text-sm text-zinc-600 dark:text-zinc-400">
            {{ __('¿Ya tienes una cuenta?') }}
            <flux:link :href="route('login')" wire:navigate
                       class="text-indigo-600 dark:text-indigo-400 font-medium hover:text-indigo-800 dark:hover:text-indigo-300">
                {{ __('Inicia sesión') }}</flux:link>
        </div>
    </div>
</div>

<!-- Script para cargar y manejar reCAPTCHA -->
<script src="https://www.google.com/recaptcha/api.js?render=explicit" async defer></script>
<script>
    // Función para renderizar reCAPTCHA
    function renderRecaptcha() {
        if (typeof grecaptcha !== 'undefined') {
            grecaptcha.ready(function () {
                grecaptcha.render('recaptcha-container', {
                    'sitekey': '{{ config("recaptcha.site_key") }}',
                    'callback': function (response) {
                        // Enviar la respuesta del captcha a Livewire
                        @this.
                        set('recaptchaResponse', response);
                    },
                    'expired-callback': function () {
                        // Limpiar la respuesta cuando expire
                        @this.
                        set('recaptchaResponse', '');
                    }
                });
            });
        }
    }

    // Inicializar reCAPTCHA cuando Livewire esté listo
    document.addEventListener('livewire:initialized', () => {
        // Cargar reCAPTCHA cuando el DOM esté listo
        window.onload = function () {
            console.log('Cargando reCAPTCHA');
            renderRecaptcha();
        };

        // Escuchar evento de email restaurado
        @this.on('email-restored', (event) => {
            // Mostrar notificación temporal mejorada
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-xl z-50 transition-all duration-300 transform translate-x-0';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="font-medium text-sm">${event.message}</span>
                </div>
            `;

            // Animar entrada
            notification.style.transform = 'translateX(100%)';
            document.body.appendChild(notification);

            // Animar hacia adentro
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Remover la notificación después de 4 segundos
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        });
    });

    // Intento adicional de renderizado si grecaptcha ya está disponible
    if (typeof grecaptcha !== 'undefined') {
        console.log('Volviendo a cargar reCAPTCHA');
        renderRecaptcha();
    }
</script>
